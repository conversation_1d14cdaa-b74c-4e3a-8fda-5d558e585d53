import { useState, useEffect, useCallback } from 'react';

interface CertificationsProps {
  language: 'vi' | 'en';
}

const Certifications = ({ language }: CertificationsProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const translations = {
    title: {
      vi: 'AN BINH FOODS CHỨNG NHẬN',
      en: 'AN BINH FOODS CERTIFICATES'
    },
    close: {
      vi: 'Đóng',
      en: 'Close'
    }
  };

  const certificates = [
    {
      id: 1,
      image: '/images/certifications/FDA.jpg',
      alt: 'FDA'
    },
    {
      id: 2,
      image: '/images/certifications/ISO.jpg',
      alt: 'ISO Certificate'
    },

    {
      id: 3,
      image: '/images/certifications/HALAL-JAKIM-2025/HALAL JAKIM 2025_page-0001.jpg',
      alt: 'Halal Certificate'
    },
    {
      id: 4,
      image: '/images/certifications/GMP.jpg',
      alt: 'GMP Certificate'
    },
    {
      id: 5,
      image: '/images/certifications/HACCP (2).jpg',
      alt: 'HACCP Certificate'
    },
        {
      id: 6,
      image: '/images/certifications/KOSHER/KOSHER_page-0001.jpg',
      alt: 'Kosher Certificate'
    }

  ];

  const visibleCertificates = () => {
    const result = [];
    for (let i = 0; i < 4; i++) {
      const index = (currentIndex + i) % certificates.length;
      result.push(certificates[index]);
    }
    return result;
  };

  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % certificates.length);
  }, [certificates.length]);

  const prevSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + certificates.length) % certificates.length);
  }, [certificates.length]);

  const openImagePopup = (imageSrc: string) => {
    setSelectedImage(imageSrc);
  };

  const closeImagePopup = () => {
    setSelectedImage(null);
  };

  // Tự động chuyển đổi giữa các chứng nhận mỗi 5 giây
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 5000);

    return () => clearInterval(interval);
  }, [nextSlide]);

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12 text-red-600 uppercase">
          {translations.title[language]}
        </h2>

        <div className="relative max-w-6xl mx-auto">
          <div className="flex justify-center items-center gap-4 md:gap-8">
            {visibleCertificates().map((cert) => (
              <div
                key={cert.id}
                className="bg-white rounded-[20px] shadow-md overflow-hidden border border-gray-200 hover:shadow-xl transition-all duration-300 p-4 w-full max-w-[250px] h-[350px] flex items-center justify-center cursor-pointer"
                onClick={() => openImagePopup(cert.image)}
              >
                <img
                  src={cert.image}
                  alt={cert.alt}
                  className="w-full h-auto object-contain max-h-[320px]"
                />
              </div>
            ))}
          </div>

          {/* Navigation arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 z-10"
            aria-label="Previous certificate"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 z-10"
            aria-label="Next certificate"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Image Popup Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={closeImagePopup}>
          <div className="relative max-w-4xl max-h-[90vh] overflow-auto bg-white rounded-lg p-2" onClick={(e) => e.stopPropagation()}>
            <img src={selectedImage} alt="Certificate" className="w-full h-auto object-contain" />
            <button
              className="absolute top-2 right-2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
              onClick={closeImagePopup}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </section>
  );
};

export default Certifications;
